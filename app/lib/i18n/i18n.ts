import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translations
import enCommon from '../../locales/en/translation.json';
import arCommon from '../../locales/ar/translation.json';
import frCommon from '../../locales/fr/translation.json';

// Use global flag to prevent multiple initializations across HMR reloads
declare global {
  var __i18n_initialized__: boolean | undefined;
}

// Initialize i18next
const initI18n = async () => {
  // Guard against multiple initializations using global flag and i18n state
  if (globalThis.__i18n_initialized__ || i18n.isInitialized) {
    // If already initialized, just update resources to handle HMR
    if (i18n.isInitialized) {
      i18n.addResourceBundle('en', 'search', enCommon.search, true, true);
      i18n.addResourceBundle('en', 'placeManagement', enCommon.placeManagement, true, true);
      i18n.addResourceBundle('ar', 'search', arCommon.search, true, true);
      i18n.addResourceBundle('ar', 'placeManagement', arCommon.placeManagement, true, true);
      i18n.addResourceBundle('fr', 'search', frCommon.search, true, true);
      i18n.addResourceBundle('fr', 'placeManagement', frCommon.placeManagement, true, true);
    }
    return i18n;
  }

  globalThis.__i18n_initialized__ = true;

  await i18n
    // detect user language
    .use(LanguageDetector)
    // pass the i18n instance to react-i18next
    .use(initReactI18next)
    // init i18next
    .init({
      debug: false, // Disable debug to reduce console noise during development
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false, // not needed for react as it escapes by default
      },
      resources: {
        en: {
          common: enCommon,
          search: enCommon.search,
          placeManagement: enCommon.placeManagement
        },
        ar: {
          common: arCommon,
          search: arCommon.search,
          placeManagement: arCommon.placeManagement
        },
        fr: {
          common: frCommon,
          search: frCommon.search,
          placeManagement: frCommon.placeManagement
        }
      },
      defaultNS: 'common',
      initAsync: true, // Ensure initialization is async
    });

  return i18n;
};

// Initialize i18n only once
if (!globalThis.__i18n_initialized__ && !i18n.isInitialized) {
  initI18n();
}

export default i18n;