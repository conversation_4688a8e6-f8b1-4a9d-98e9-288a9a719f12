/**
 * Map Provider Context
 *
 * Simplified context provider for map state management.
 * Provides clean API for map functionality without complex dependencies.
 */

import React, { createContext, useContext, useRef, useCallback, useEffect, useMemo } from 'react';
import { useMapState, useSetMapReady, useSetInteracting } from '~/hooks/useMapState';
import { useMapFeatures, type MapFeature } from '~/hooks/useMapFeatures';
import { useRoutesV2 } from '~/hooks/useRoutesV2';
import { useWanderlustStore } from '~/stores/wanderlust';
import type { VisitedPlace } from '~/types/wanderlust';

export interface MapContextValue {
  // Map instance
  mapRef: React.MutableRefObject<google.maps.Map | null>;

  // State
  center: { lat: number; lng: number };
  zoom: number;
  places?: VisitedPlace[];
  selectedPlace: VisitedPlace | null;
  isMapReady: boolean;
  isInteracting: boolean;
  mapType: 'roadmap' | 'satellite';
  showTraffic: boolean;

  // Actions
  setCenter: (center: { lat: number; lng: number }) => void;
  setZoom: (zoom: number) => void;
  setPlaces: (places: VisitedPlace[]) => void;
  setSelectedPlace: (place: VisitedPlace | null) => void;
  setMapReady: (ready: boolean) => void;
  setInteracting: (interacting: boolean) => void;
  setMapType: (type: 'roadmap' | 'satellite') => void;
  setShowTraffic: (show: boolean) => void;
  panTo: (lat: number, lng: number, zoom?: number) => void;
  fitBounds: (places: VisitedPlace[]) => void;
  reset: () => void;

  // Features
  enabledFeatures: any;
  toggleFeature: (feature: MapFeature) => void;
  enableFeature: (feature: MapFeature) => void;
  disableFeature: (feature: MapFeature) => void;
  isFeatureEnabled: (feature: MapFeature) => boolean;

  // Routes (from Routes v2)
  currentRoute: any;
  alternativeRoutes: any[];
  routeDetails: any;
  isCalculatingRoute: boolean;
  calculateRoute: (waypoints: any[], mode?: any, options?: any) => Promise<any>;
  clearRoute: () => void;
  getTurnByTurnDirections: () => string[];

  // Utilities
  getMap: () => google.maps.Map | null;
  isGoogleMapsLoaded: () => boolean;
}

const MapContext = createContext<MapContextValue | null>(null);

export interface MapProviderProps {
  children: React.ReactNode;
  features?: MapFeature[];
  onMapLoad?: (map: google.maps.Map) => void;
  onError?: (error: Error) => void;
  initialPlaces?: VisitedPlace[];
}

export function MapProvider({
  children,
  features = ['markers'],
  onMapLoad,
  onError,
  initialPlaces = []
}: MapProviderProps) {
  const mapRef = useRef<google.maps.Map | null>(null);

  // Debug logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log('🗺️ MapProvider: Rendering with features:', features);
  }

  // Map state
  const {
    center,
    zoom,
    selectedPlace,
    isMapReady,
    isInteracting,
    mapType,
    showTraffic,
  } = useMapState();

  // Individual action hooks to prevent infinite loops
  const setMapReady = useSetMapReady();
  const setInteracting = useSetInteracting();

  // Get individual stable action hooks for context value
  const setCenter = useMapState(state => state.setCenter);
  const setZoom = useMapState(state => state.setZoom);
  const setPlaces = useMapState(state => state.setPlaces);
  const setSelectedPlace = useMapState(state => state.setSelectedPlace);
  const setMapType = useMapState(state => state.setMapType);
  const setShowTraffic = useMapState(state => state.setShowTraffic);
  const panTo = useMapState(state => state.panTo);
  const fitBounds = useMapState(state => state.fitBounds);
  const reset = useMapState(state => state.reset);

  // Features
  const {
    enabledFeatures,
    toggleFeature,
    enableFeature,
    disableFeature,
    isFeatureEnabled,
  } = useMapFeatures(features);

  // Routes v2 integration
  const {
    currentRoute: routesV2CurrentRoute,
    alternativeRoutes: routesV2AlternativeRoutes,
    routeDetails,
    isCalculating: isCalculatingRoute,
    calculateRoute,
    clearRoute: clearRoutesV2Route,
    getTurnByTurnDirections,
  } = useRoutesV2();

  // Also get route data from Wanderlust store for compatibility
  const {
    currentRoute: storeCurrentRoute
  } = useWanderlustStore();

  // Use Routes V2 route if available, otherwise fall back to store route
  const currentRoute = routesV2CurrentRoute || storeCurrentRoute;
  const alternativeRoutes = routesV2AlternativeRoutes || [];

  // Debug logging for route data flow
  if (process.env.NODE_ENV === 'development') {
    console.log('🗺️ MapProvider: Route data', {
      routesV2CurrentRoute: !!routesV2CurrentRoute,
      storeCurrentRoute: !!storeCurrentRoute,
      finalCurrentRoute: !!currentRoute,
      currentRoutePolyline: currentRoute?.polyline?.length || 0,
      alternativeRoutesCount: alternativeRoutes.length
    });
  }

  // Clear route function that clears both systems
  const clearRoute = useCallback(() => {
    clearRoutesV2Route();
    const { clearRoute: clearStoreRoute } = useWanderlustStore.getState();
    clearStoreRoute();
  }, [clearRoutesV2Route]);

  // Map utilities
  const getMap = useCallback(() => mapRef.current, []);

  const isGoogleMapsLoaded = useCallback(() => {
    return typeof window !== 'undefined' && !!window.google?.maps;
  }, []);

  // Handle map load
  const handleMapLoad = useCallback((map: google.maps.Map) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🗺️ MapProvider: Map loaded, setting ready state');
    }

    mapRef.current = map;
    setMapReady(true);

    // Set up basic event listeners
    const handleDragStart = () => setInteracting(true);
    const handleDragEnd = () => setInteracting(false);

    map.addListener('dragstart', handleDragStart);
    map.addListener('dragend', handleDragEnd);
    map.addListener('zoom_changed', handleDragStart);
    map.addListener('idle', handleDragEnd);

    // Notify parent component
    onMapLoad?.(map);
  }, [setMapReady, setInteracting, onMapLoad]);

  // Cleanup on unmount - use individual action functions as dependencies
  useEffect(() => {
    return () => {
      if (mapRef.current) {
        // Clean up event listeners
        google.maps.event.clearInstanceListeners(mapRef.current);
        mapRef.current = null;
      }
      setMapReady(false);
    };
  }, [setMapReady]);

  // Error handling
  const handleError = useCallback((error: Error) => {
    console.error('Map error:', error);
    onError?.(error);
  }, [onError]);

  // Memoize context value to prevent infinite re-renders
  const contextValue: MapContextValue = useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🗺️ MapProvider: Creating new context value', {
        isMapReady,
        currentRoute: !!currentRoute,
        enabledFeatures,
      });
    }

    return {
      // Map instance
      mapRef,

      // State
      center,
      zoom,
      places: initialPlaces,
      selectedPlace,
      isMapReady,
      isInteracting,
      mapType,
      showTraffic,

      // Actions (using individual stable hooks)
      setCenter,
      setZoom,
      setPlaces,
      setSelectedPlace,
      setMapReady,
      setInteracting,
      setMapType,
      setShowTraffic,
      panTo,
      fitBounds,
      reset,

      // Features
      enabledFeatures,
      toggleFeature,
      enableFeature,
      disableFeature,
      isFeatureEnabled,

      // Routes
      currentRoute,
      alternativeRoutes,
      routeDetails,
      isCalculatingRoute,
      calculateRoute,
      clearRoute,
      getTurnByTurnDirections,

      // Utilities
      getMap,
      isGoogleMapsLoaded,
    };
  }, [
    // Map instance (stable ref)
    mapRef,

    // State values
    center,
    zoom,
    // places, // 'places' is managed by useMapState and accessed via useMapPlaces hook
    selectedPlace,
    isMapReady,
    isInteracting,
    mapType,
    showTraffic,

    // Actions (stable individual hooks)
    setCenter,
    setZoom,
    setPlaces,
    setSelectedPlace,
    setMapReady,
    setInteracting,
    setMapType,
    setShowTraffic,
    panTo,
    fitBounds,
    reset,

    // Features (memoized in useMapFeatures)
    enabledFeatures,
    toggleFeature,
    enableFeature,
    disableFeature,
    isFeatureEnabled,

    // Routes (from useRoutesV2)
    currentRoute,
    alternativeRoutes,
    routeDetails,
    isCalculatingRoute,
    calculateRoute,
    clearRoute,
    getTurnByTurnDirections,

    // Utilities (stable callbacks)
    getMap,
    isGoogleMapsLoaded,
  ]);

  return (
    <MapContext.Provider value={contextValue}>
      {children}
    </MapContext.Provider>
  );
}

// Hook to use the map context
export function useMapContext(): MapContextValue {
  const context = useContext(MapContext);

  if (!context) {
    throw new Error('useMapContext must be used within a MapProvider');
  }

  return context;
}

// Convenience hooks for specific parts of the context
export const useMapInstance = () => {
  const { mapRef, getMap } = useMapContext();
  return { mapRef, getMap };
};

export const useMapFeatureState = () => {
  const { enabledFeatures, toggleFeature, enableFeature, disableFeature, isFeatureEnabled } = useMapContext();
  return { enabledFeatures, toggleFeature, enableFeature, disableFeature, isFeatureEnabled };
};

export const useMapRoutes = () => {
  const { currentRoute, alternativeRoutes, routeDetails, isCalculatingRoute, calculateRoute, clearRoute, getTurnByTurnDirections } = useMapContext();
  return { currentRoute, alternativeRoutes, routeDetails, isCalculatingRoute, calculateRoute, clearRoute, getTurnByTurnDirections };
};
