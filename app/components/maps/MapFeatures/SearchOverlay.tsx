/**
 * Search Overlay Component
 *
 * Integrates with simplified useGooglePlaces hook to provide search functionality
 * with FIFA design system styling and mobile-first responsive design.
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Search, X, MapPin, Clock } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { useMapContext } from '../MapProvider';
import { useGooglePlaces } from '~/hooks/useGooglePlaces';
import type { PlaceSearchResult } from '~/hooks/useGooglePlaces';
import { cn } from '~/lib/utils';

export interface SearchOverlayProps {
  onPlaceSelect?: (place: PlaceSearchResult) => void;
  onClose?: () => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function SearchOverlay({
  onPlaceSelect,
  onClose,
  placeholder = "Search for places...",
  className,
  autoFocus = false,
}: SearchOverlayProps) {
  const [query, setQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  const {
    getMap,
    isFeatureEnabled,
    panTo,
    setSelectedPlace,
  } = useMapContext();

  // Check if search feature is enabled
  const shouldShowSearch = isFeatureEnabled('search');

  // Initialize Google Places with map instance
  const map = getMap();
  const {
    searchPlaces,
    autocompleteResults,
    searchResults,
    isSearching,
    searchHistory,
    clearSearchResults,
  } = useGooglePlaces(map || undefined);

  // Handle input focus
  const handleFocus = useCallback(() => {
    setIsExpanded(true);
    setShowResults(true);
  }, []);

  // Handle input blur (with delay to allow clicks)
  const handleBlur = useCallback(() => {
    setTimeout(() => {
      setShowResults(false);
      if (!query) {
        setIsExpanded(false);
      }
    }, 150);
  }, [query]);

  // Handle search input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    if (value.trim()) {
      // Trigger autocomplete search
      searchPlaces(value);
    } else {
      clearSearchResults();
    }
  }, [searchPlaces, clearSearchResults]);

  // Handle place selection
  const handlePlaceSelect = useCallback(async (place: PlaceSearchResult) => {
    setQuery(place.name);
    setShowResults(false);
    setIsExpanded(false);

    // Pan to the selected place
    panTo(
      place.geometry.location.lat(),
      place.geometry.location.lng(),
      15
    );

    // Convert to VisitedPlace format for selection
    const visitedPlace = {
      id: place.place_id,
      name: place.name,
      description: { en: place.formatted_address, fr: '', ar: '' },
      coordinates: {
        latitude: place.geometry.location.lat(),
        longitude: place.geometry.location.lng(),
      },
      category: 'landmark' as const,
      rating: place.rating || 0,
      visitDate: new Date().toISOString(),
      icon: '📍',
      city: '',
      region: '',
      revisitPotential: 'Worth a Look' as const,
    };

    setSelectedPlace(visitedPlace);
    onPlaceSelect?.(place);
  }, [panTo, setSelectedPlace, onPlaceSelect]);

  // Handle clear search
  const handleClear = useCallback(() => {
    setQuery('');
    setShowResults(false);
    setIsExpanded(false);
    clearSearchResults();
    inputRef.current?.blur();
  }, [clearSearchResults]);

  // Handle close
  const handleClose = useCallback(() => {
    handleClear();
    onClose?.();
  }, [handleClear, onClose]);

  // Auto focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowResults(false);
        if (!query) {
          setIsExpanded(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [query]);

  if (!shouldShowSearch) {
    return null;
  }

  return (
    <div className={cn(
      "absolute top-4 left-4 right-4 z-30 fifa-search-overlay",
      className
    )}>
      {/* Search Input */}
      <div className={cn(
        "relative bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 transition-all duration-200",
        isExpanded ? "ring-2 ring-[#FFD700]/50" : ""
      )}>
        <div className="flex items-center gap-2 p-3">
          <Search className="w-5 h-5 text-gray-400 flex-shrink-0" />
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            className="border-0 bg-transparent p-0 text-sm focus:ring-0 fifa-search-input"
          />
          {(query || isExpanded) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="p-1 h-auto fifa-search-clear"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Search Results */}
        {showResults && (
          <div
            ref={resultsRef}
            className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 max-h-80 overflow-y-auto fifa-search-results"
          >
            {isSearching && (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#FFD700] mx-auto mb-2"></div>
                Searching...
              </div>
            )}

            {/* Autocomplete Results */}
            {!isSearching && autocompleteResults.length > 0 && (
              <div className="py-2">
                <div className="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Suggestions
                </div>
                {autocompleteResults.map((result) => (
                  <button
                    key={result.place_id}
                    onClick={() => {
                      // Convert autocomplete result to search result format
                      const searchResult: PlaceSearchResult = {
                        place_id: result.place_id,
                        name: result.structured_formatting.main_text,
                        formatted_address: result.description,
                        geometry: {
                          location: new google.maps.LatLng(0, 0), // Will be geocoded
                          viewport: new google.maps.LatLngBounds(),
                        },
                        types: result.types,
                      };
                      handlePlaceSelect(searchResult);
                    }}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3 fifa-search-result"
                  >
                    <MapPin className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {result.structured_formatting.main_text}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {result.structured_formatting.secondary_text}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Search History */}
            {!isSearching && !query && searchHistory.length > 0 && (
              <div className="py-2">
                <div className="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Recent Searches
                </div>
                {searchHistory.slice(0, 5).map((historyItem, index) => (
                  <button
                    key={index}
                    onClick={() => setQuery(historyItem)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3 fifa-search-history"
                  >
                    <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{historyItem}</span>
                  </button>
                ))}
              </div>
            )}

            {/* No Results */}
            {!isSearching && query && autocompleteResults.length === 0 && (
              <div className="p-4 text-center text-gray-500">
                <MapPin className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No places found for "{query}"</p>
                <p className="text-xs text-gray-400 mt-1">Try a different search term</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
