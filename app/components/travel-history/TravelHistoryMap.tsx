// app/components/travel-history/TravelHistoryMap.tsx
import { BaseMapComponent, type BaseMapComponentProps, type MarkerContext } from '~/components/maps/BaseMapComponent';
import type { VisitedPlace } from '~/types/wanderlust';
import { useWanderlustStore } from '~/stores/wanderlust';

// Category colors for travel history markers (FIFA theme)
const categoryColors: Record<string, string> = {
  restaurant: '#DC2626', // FIFA Red
  hotel: '#FFD700', // FIFA Gold
  attraction: '#000000', // FIFA Black
  transport: '#1F2937', // Dark Gray
  shopping: '#059669', // Green
  entertainment: '#7C3AED', // Purple
  default: '#6B7280', // Gray
};

// Custom marker renderer for travel history
const travelHistoryMarkerRenderer = (place: VisitedPlace, context: MarkerContext) => {
  const color = categoryColors[place.category] || categoryColors.default;
  const isSelected = context.isSelected;

  // Use Google Maps Symbol with FIFA styling
  return {
    icon: {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor: color,
      fillOpacity: isSelected ? 1.0 : 0.8,
      strokeColor: '#FFD700', // FIFA Gold border
      strokeWeight: isSelected ? 3 : 2,
      scale: isSelected ? 14 : 10,
      anchor: new google.maps.Point(0, 0),
    },
    title: place.name,
  };
};

// Placeholder handler for travel history place selection
const handleTravelHistorySelection = (place: VisitedPlace) => {
  console.log('Travel history place selected on map:', place.name);
};

export function TravelHistoryMap(props: BaseMapComponentProps) {
  // Access filteredPlaces from the store
  const { filteredPlaces } = useWanderlustStore();

  // Debug logging for travel history marker visibility
  console.log('🗺️ TravelHistoryMap: Places count:', filteredPlaces?.length || 0);

  // Create a test marker if no places are available (for debugging)
  const testPlace: VisitedPlace = {
    id: 'test-travel-history-marker',
    name: 'Test Travel History Marker',
    category: 'attraction',
    coordinates: { latitude: 36.1627, longitude: -86.7816 }, // Nashville coordinates
    description: {
      en: 'Test marker to verify travel history map functionality',
      fr: 'Marqueur de test pour vérifier la fonctionnalité de la carte d\'historique de voyage',
      ar: 'علامة اختبار للتحقق من وظائف خريطة تاريخ السفر'
    },
    city: 'Nashville',
    region: 'Tennessee',
    icon: '🏛️',
    rating: 4.5,
    revisitPotential: 'Worth a Look',
    keyTakeaway: 'Test marker for debugging travel history display',
    personalNotes: 'This is a test marker to verify the travel history rendering pipeline',
    visitDate: new Date().toISOString().split('T')[0]
  };

  // Use filtered places if available, otherwise use test data for debugging
  const placesToRender = filteredPlaces && filteredPlaces.length > 0 ? filteredPlaces : [testPlace];

  console.log('🎯 TravelHistoryMap: Rendering places:', placesToRender.map(p => p.name));

  return (
    <BaseMapComponent
      features={['markers']} // Enable markers feature for travel history
      customMarkerRenderer={travelHistoryMarkerRenderer}
      onPlaceSelect={handleTravelHistorySelection}
      places={placesToRender} // Pass places to render (filtered or test data)
      {...props} // Spread the received props
    >
      {/* Map-specific layers/features for travel history can go here if needed */}
    </BaseMapComponent>
  );
}

export default TravelHistoryMap;
