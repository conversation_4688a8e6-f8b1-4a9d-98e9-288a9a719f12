// app/components/venue-discovery/VenueExplorerMap.tsx
import { BaseMapComponent, type BaseMapComponentProps, type MarkerContext } from '~/components/maps/BaseMapComponent'; // Import BaseMapComponent, its props type, and MarkerContext
import type { VisitedPlace } from '~/types/wanderlust'; // Assuming VisitedPlace type is here
import { useWanderlustStore } from '~/stores/wanderlust'; // Import the store

// Custom marker renderer for venues
const venueMarkerRenderer = (place: VisitedPlace, context: MarkerContext) => {
  console.log('🎯 Rendering venue marker for:', place.name, 'Category:', place.category, 'Context:', context);

  // Category-based marker colors
  const categoryColors: Record<string, string> = {
    restaurant: '#FF6B6B',
    attraction: '#4ECDC4',
    hotel: '#45B7D1',
    shopping: '#96CEB4',
    entertainment: '#FFEAA7',
    transport: '#DDA0DD',
    default: '#FFD700'
  };

  const color = categoryColors[place.category] || categoryColors.default;
  const isSelected = context.isSelected;

  // Use Google Maps Symbol instead of potentially invalid emoji URLs
  return {
    icon: {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor: color,
      fillOpacity: isSelected ? 1.0 : 0.8,
      strokeColor: '#FFFFFF',
      strokeWeight: isSelected ? 3 : 2,
      scale: isSelected ? 14 : 10,
      anchor: new google.maps.Point(0, 0),
    },
    title: place.name,
  };
};

// Placeholder components for layers and overlays
const VenueDetailsOverlay = () => {
  // TODO: Implement venue details overlay - this might consume selected place data
  return null;
};

// Placeholder handler for venue selection
const handleVenueSelection = (place: VisitedPlace) => {
  // TODO: Implement logic for handling venue selection on the map (e.g., update store, show details panel)
  console.log('Venue selected on map:', place.name);
};

export function VenueExplorerMap(props: BaseMapComponentProps) {
  // Access filteredPlaces from the store
  const { filteredPlaces } = useWanderlustStore();

  // Debug logging for venue marker visibility
  console.log('🗺️ VenueExplorerMap: Places count:', filteredPlaces?.length || 0);

  // Create a test marker if no places are available (for debugging)
  const testPlace: VisitedPlace = {
    id: 'test-marker',
    name: 'Test Venue Marker',
    category: 'restaurant',
    coordinates: { latitude: 36.1627, longitude: -86.7816 }, // Nashville coordinates
    description: {
      en: 'Test marker to verify map functionality',
      fr: 'Marqueur de test pour vérifier la fonctionnalité de la carte',
      ar: 'علامة اختبار للتحقق من وظائف الخريطة'
    },
    city: 'Nashville',
    region: 'Tennessee',
    icon: '🍕',
    rating: 4.5,
    revisitPotential: 'Worth a Look',
    keyTakeaway: 'Test marker for debugging venue display',
    personalNotes: 'This is a test marker to verify the venue rendering pipeline',
    visitDate: new Date().toISOString().split('T')[0]
  };

  // Use test data if no filtered places available
  const placesToRender = filteredPlaces && filteredPlaces.length > 0 ? filteredPlaces : [testPlace];

  if (placesToRender.length === 1 && placesToRender[0].id === 'test-marker') {
    console.log('🚨 VenueExplorerMap: Using test marker - no real venue data available');
  }

  return (
    <BaseMapComponent
      features={['markers', 'search']} // Features for venue discovery (removed 'clustering')
      customMarkerRenderer={venueMarkerRenderer}
      onPlaceSelect={handleVenueSelection}
      places={placesToRender} // Pass places to render (filtered or test data)
      {...props} // Spread the received props
    >
      {/* Layers and Overlays specific to Venue Discovery */}
      {/* These layers might need access to filteredPlaces as well */}
      {/* VenueClusterLayer is a placeholder and might interfere if 'clustering' feature is enabled */}
      {/* <VenueClusterLayer /> */}
      <VenueDetailsOverlay />
    </BaseMapComponent>
  );
}
