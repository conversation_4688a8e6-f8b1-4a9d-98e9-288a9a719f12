import { useEffect, useState, useCallback } from 'react';
import { BaseMapComponent } from '~/components/maps/BaseMapComponent'; // Import BaseMapComponent
import { useMapContext } from '~/components/maps/MapProvider'; // Keep MapProvider context for now if needed for state
import { useWanderlustStore } from '~/stores/wanderlust';
import { cn } from '~/lib/utils';
import type { VisitedPlace } from '~/types/wanderlust';

interface RouteMapProps {
  onMapLoad?: () => void;
}

export function RouteMap({ onMapLoad }: RouteMapProps = {}) {
  const [isClient, setIsClient] = useState(false);

  const {
    itinerary,
  } = useWanderlustStore();

  // Get map context from the parent MapProvider (adjust if MapProvider is refactored)
  const {
    center,
    zoom,
    mapType,
    showTraffic,
    setMapReady, // setMapReady might be handled by BaseMapComponent internally
    setInteracting, // setInteracting might be handled by BaseMapComponent internally
    setSelectedPlace,
  } = useMapContext();

  // Check if client-side to prevent hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle map load (adjust to work with BaseMapComponent's onMapLoad)
  const handleMapLoad = useCallback((map: google.maps.Map) => {
    console.log('🗺️ Route planner map loaded successfully', {
      center,
      zoom,
      itineraryLength: itinerary.length,
      mapContainer: map.getDiv()
    });

    // Basic event listeners might be handled by BaseMapComponent or useMapInteractions hook
    // const handleDragStart = () => setInteracting(true);
    // const handleDragEnd = () => setInteracting(false);
    // map.addListener('dragstart', handleDragStart);
    // map.addListener('dragend', handleDragEnd);
    // map.addListener('zoom_changed', handleDragStart);
    // map.addListener('idle', handleDragEnd);

    onMapLoad?.();
    // showSuccess/showError might be handled by a global error boundary or notification system
    // showSuccess('Map Ready', 'Route planner map loaded successfully');

    // setMapReady(true); // Handled by BaseMapComponent
  }, [onMapLoad, center, zoom, itinerary.length]); // Removed setMapReady, setInteracting

  // Handle place selection (pass as onPlaceSelect prop to BaseMapComponent)
  const handlePlaceSelect = useCallback((place: VisitedPlace) => {
    console.log('📍 Place selected in route map:', place.name);
    setSelectedPlace(place);
  }, [setSelectedPlace]);

  // Handle map click (pass as onMapClick prop to BaseMapComponent)
  const handleMapClick = useCallback(() => {
    setSelectedPlace(null);
  }, [setSelectedPlace]);

  // Map options (pass as customStyles prop to BaseMapComponent if needed, or configure via features)
  const mapOptions: google.maps.MapOptions = {
    disableDefaultUI: true,
    zoomControl: true,
    mapTypeControl: false,
    streetViewControl: false,
    fullscreenControl: false,
    gestureHandling: 'greedy',
    backgroundColor: '#000000',
    mapTypeId: mapType,
  };

  // Custom marker renderer for itinerary places (pass as customMarkerRenderer prop to BaseMapComponent)
  const routeWaypointRenderer = useCallback((place: VisitedPlace, context: { index?: number, totalCount: number }) => {
    const index = context.index; // Use index from context
    // Add check for index being defined
    const isStart = index !== undefined && index === 0;
    const isEnd = index !== undefined && index === context.totalCount - 1;
    const color = isStart ? '#10B981' : isEnd ? '#EF4444' : '#FFD700';
    const label = isStart ? 'S' : isEnd ? 'E' : (index !== undefined ? (index + 1).toString() : ''); // Handle undefined index

    return {
      icon: {
        url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
          <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="18" fill="${color}" stroke="#000" stroke-width="2"/>
            <text x="20" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="${isStart || isEnd ? '#FFF' : '#000'}">
              ${label}
            </text>
          </svg>
        `)}`,
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 20),
      },
      title: place.name, // Add title for accessibility
    };
  }, []);


  // Show loading state during hydration (BaseMapComponent should handle this)
  if (!isClient) {
    console.log('🔄 RouteMap: Client-side hydration not complete');
    return (
      <div className="h-full bg-gray-100 rounded-lg overflow-hidden shadow-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FFD700] mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Initializing map...</p>
        </div>
      </div>
    );
  }

  // BaseMapComponent should handle loading and error states internally
  // if (loadError) { ... }
  // if (!isGoogleMapsLoaded) { ... }

  console.log('✅ RouteMap: Rendering route planner map', {
    center,
    zoom,
    itineraryLength: itinerary.length
  });

  return (
    <div className="h-full w-full relative" style={{ minHeight: '400px' }}>
      {/* Use BaseMapComponent */}
      <BaseMapComponent
        initialCenter={center} // Changed to initialCenter
        initialZoom={zoom} // Changed to initialZoom
        mapType={mapType}
        features={['markers', 'routes', showTraffic ? 'traffic' : undefined].filter(Boolean) as any} // Configure features
        places={itinerary} // Pass itinerary places to BaseMapComponent
        customMarkerRenderer={routeWaypointRenderer} // Use custom renderer
        onMapLoad={handleMapLoad} // Pass map load handler
        onPlaceSelect={handlePlaceSelect} // Pass place select handler
        onMapClick={handleMapClick} // Pass map click handler
        mode="route-planning" // Set mode for route planning specific config
      >
        {/* Children can be used for additional layers or components if needed */}
        {/* <RouteLayer ... /> // RouteLayer logic might be integrated into BaseMapComponent features or passed as children */}
      </BaseMapComponent>


      {/* Simple Empty State (Keep if not handled by BaseMapComponent) */}
      {itinerary.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm z-10">
          <div className="text-center text-white">
            <div className="text-4xl mb-4">🗺️</div>
            <h3 className="text-xl font-bold mb-2">Start Planning Your Route</h3>
            <p className="text-white/70 max-w-md">
              Add waypoints using the route builder to begin planning your FIFA Club World Cup 2025™ journey
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
