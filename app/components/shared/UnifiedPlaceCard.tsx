/**
 * Unified Place Card Component
 *
 * Configurable place display component that adapts to different contexts:
 * - search-result: Shows add button, rating, distance
 * - waypoint: Shows reorder handles, remove button, route index
 * - venue-detail: Shows full details, favorite button, navigation
 */

import React, { useState, useCallback } from 'react';
import { Star, Plus, Minus, Heart, MapPin, MoreHorizontal, GripVertical, Navigation, Clock } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { cn } from '~/lib/utils';
import { useGestures } from '~/hooks/useGestures';
import type { VisitedPlace } from '~/types/wanderlust';
import { useTranslation } from 'react-i18next';

export type PlaceCardVariant = 'search-result' | 'waypoint' | 'venue-detail';

export interface PlaceCardAction {
  type: 'add' | 'remove' | 'reorder' | 'favorite' | 'navigate' | 'details';
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: (place: VisitedPlace) => void;
  disabled?: boolean;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost';
}

export interface UnifiedPlaceCardProps {
  place: VisitedPlace;
  variant: PlaceCardVariant;
  actions: PlaceCardAction[];
  showDetails?: boolean;
  index?: number;
  isSelected?: boolean;
  isFavorite?: boolean;
  distance?: number;
  estimatedTime?: string;
  className?: string;
  onSelect?: (place: VisitedPlace) => void;
  enableSwipeActions?: boolean;
  enableReordering?: boolean;
}

// Category information with FIFA design system colors
// Category information with FIFA design system colors (Labels will be translated)
const categories = [
  { id: 'food', labelKey: 'category.food', icon: '🍽️', color: '#FFD700' },
  { id: 'landmark', labelKey: 'category.landmark', icon: '🏛️', color: '#DC2626' },
  { id: 'museum', labelKey: 'category.museum', icon: '🏛️', color: '#1E40AF' },
  { id: 'park', labelKey: 'category.park', icon: '🌳', color: '#059669' },
  { id: 'accommodation', labelKey: 'category.accommodation', icon: '🏨', color: '#7C2D12' },
  { id: 'transport', labelKey: 'category.transport', icon: '🚇', color: '#374151' },
  { id: 'entertainment', labelKey: 'category.entertainment', icon: '🎭', color: '#BE185D' },
  { id: 'shopping', labelKey: 'category.shopping', icon: '🛍️', color: '#EA580C' },
];

export function UnifiedPlaceCard({
  place,
  variant,
  actions,
  showDetails = false,
  index,
  isSelected = false,
  isFavorite = false,
  distance,
  estimatedTime,
  className,
  onSelect,
  enableSwipeActions = false,
  enableReordering = false,
}: UnifiedPlaceCardProps) {
  const { t } = useTranslation('common'); // Use the translation hook
  const [isExpanded, setIsExpanded] = useState(showDetails);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isSwipeActionTriggered, setIsSwipeActionTriggered] = useState(false);

  // Find category info using the place's category ID
  const categoryInfo = categories.find(c => c.id === place.category) || { labelKey: 'category.other', icon: '❓', color: '#6B7280' };

  // Handle card selection
  const handleCardClick = useCallback(() => {
    onSelect?.(place);
    if (variant === 'venue-detail') {
      setIsExpanded(!isExpanded);
    }
  }, [onSelect, place, variant, isExpanded]);

  // Handle expand toggle
  const handleExpandToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  // Gesture handling for swipe actions
  const { ref: gestureRef } = useGestures({
    onSwipeLeft: () => {
      if (enableSwipeActions && actions.length > 0) {
        const primaryAction = actions[0];
        primaryAction.onClick(place);
        setIsSwipeActionTriggered(true);
        setTimeout(() => setIsSwipeActionTriggered(false), 300);
      }
    },
    onSwipeRight: () => {
      if (enableSwipeActions && actions.length > 1) {
        const secondaryAction = actions[1];
        secondaryAction.onClick(place);
        setIsSwipeActionTriggered(true);
        setTimeout(() => setIsSwipeActionTriggered(false), 300);
      }
    },
    config: {
      swipeThreshold: 50,
      velocityThreshold: 0.3,
      snapAnimationDuration: 200,
    },
    disabled: !enableSwipeActions,
  });

  // Render variant-specific content
  const renderVariantContent = () => {
    switch (variant) {
      case 'waypoint':
        return (
          <div className="flex items-center gap-2">
            {enableReordering && (
              <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
            )}
            {index !== undefined && (
              <Badge variant="outline" className="bg-[#FFD700] text-black border-[#FFD700] min-w-[24px] h-6 flex items-center justify-center">
                {index + 1}
              </Badge>
            )}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white text-sm truncate">
                {place.name} {/* Place names are dynamic, not translated via i18n */}
              </h3>
              <p className="text-xs text-white/70 truncate">{place.city}</p> {/* City names are dynamic */}
            </div>
            {estimatedTime && (
              <div className="flex items-center text-xs text-white/70">
                <Clock className="h-3 w-3 mr-1" />
                {estimatedTime}
              </div>
            )}
          </div>
        );

      case 'search-result':
        return (
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white text-sm mb-1 truncate">
                {place.name}
              </h3>
              <div className="flex items-center gap-2 text-xs text-white/70">
                <Badge
                  variant="outline"
                  className="text-xs px-1 py-0 border-white/20"
                  style={{ borderColor: categoryInfo.color + '40', color: categoryInfo.color }}
                >
                  <span className="emoji-safe">{categoryInfo.icon}</span> {t(categoryInfo.labelKey)} {/* Translate category label */}
                </Badge>
                {place.rating && (
                  <div className="flex items-center">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="ml-1">{place.rating}</span>
                  </div>
                )}
                {distance && (
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span>{distance}km</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'venue-detail':
      default:
        return (
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white text-sm lg:text-base mb-1 truncate">
                {place.name}
              </h3>
              <div className="flex items-center gap-2 text-xs text-white/70">
                <Badge
                  variant="outline"
                  className="text-xs px-1 py-0 border-white/20"
                  style={{ borderColor: categoryInfo.color + '40', color: categoryInfo.color }}
                >
                  <span className="emoji-safe">{categoryInfo.icon}</span> {t(categoryInfo.labelKey)} {/* Translate category label */}
                </Badge>
                {place.rating && (
                  <div className="flex items-center">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="ml-1">{place.rating}</span>
                  </div>
                )}
                <span className="truncate">{place.city}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              {isFavorite && <Heart className="h-4 w-4 text-red-500 fill-current" />}
              {variant === 'venue-detail' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleExpandToggle}
                  className="h-6 w-6 p-0 text-white/70 hover:text-white"
                >
                  {isExpanded ? <MoreHorizontal className="h-3 w-3" /> : <MoreHorizontal className="h-3 w-3" />}
                </Button>
              )}
            </div>
          </div>
        );
    }
  };

  // Render actions (Action labels will be translated)
  const renderActions = () => {
    if (actions.length === 0) return null;

    return (
      <div className="flex gap-2 mt-3">
        {actions.map((action, actionIndex) => (
          <Button
            key={`${action.type}-${actionIndex}`}
            size="sm"
            variant={action.variant || 'default'}
            onClick={(e) => {
              e.stopPropagation();
              action.onClick(place);
            }}
            disabled={action.disabled}
            className={cn(
              "min-h-[32px] px-3 text-xs",
              action.variant === 'default' && "bg-[#FFD700] text-black hover:bg-[#FFD700]/90",
              action.variant === 'destructive' && "bg-red-600 hover:bg-red-700"
            )}
          >
            <action.icon className="h-3 w-3 mr-1" />
            {t(action.label)} {/* Translate action label */}
          </Button>
        ))}
      </div>
    );
  };

  return (
    <Card
      ref={enableSwipeActions ? gestureRef as React.RefObject<HTMLDivElement> : undefined}
      className={cn(
        "bg-white/5 border-white/10 backdrop-blur-sm transition-all duration-200 cursor-pointer",
        isSelected && "ring-2 ring-[#FFD700] border-[#FFD700]/50",
        isSwipeActionTriggered && "scale-95",
        variant === 'waypoint' && "bg-gradient-to-r from-green-900/20 to-transparent",
        className
      )}
      onClick={handleCardClick}
      style={{
        transform: swipeOffset !== 0 ? `translateX(${swipeOffset}px)` : undefined,
      }}
    >
      <CardHeader className="pb-2 p-3">
        {renderVariantContent()}
        {renderActions()}
      </CardHeader>

      {/* Expanded Content */}
      {isExpanded && variant === 'venue-detail' && (
        <CardContent className="pt-0 p-3">
          {/* Assuming description is already localized in the data */}
          <p className="text-xs text-white/70 mb-3 leading-relaxed">
            {place.description.en} {/* Use appropriate language key */}
          </p>

          {place.keyTakeaway && (
            <div className="bg-blue-900/20 border-l-4 border-blue-400 p-3 mb-3 rounded-r">
              <p className="text-xs text-blue-300">
                💡 <strong>{t('keyTakeaway')}:</strong> {place.keyTakeaway} {/* Translate "Key Takeaway" */}
              </p>
            </div>
          )}

          {distance && estimatedTime && (
            <div className="flex items-center gap-4 text-xs text-white/70 mb-3">
              <div className="flex items-center">
                <MapPin className="h-3 w-3 mr-1" />
                {t('distanceAway', { distance })} {/* Translate "distance away" */}
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {estimatedTime} {/* Estimated time might need formatting/localization */}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
