import React, { useEffect, lazy, Suspense } from 'react';
import { useLoaderData } from 'react-router';
import type { LoaderFunctionArgs } from 'react-router';
import { MapProvider } from '~/components/maps/MapProvider';
import type { MapFeature } from '~/hooks/useMapFeatures';
import SkeletonLoader from '~/components/SkeletonLoader';
import { useWanderlustStore } from '~/stores/wanderlust';

const VenueExplorerMap = lazy(() => import('~/components/venue-discovery/VenueExplorerMap').then(module => ({ default: module.VenueExplorerMap })));
const VenueDiscoveryPanel = lazy(() => import('~/components/venue-discovery/VenueDiscoveryPanel').then(module => ({ default: module.VenueDiscoveryPanel })));
const TravelHistoryMap = lazy(() => import('~/components/travel-history/TravelHistoryMap').then(module => ({ default: module.TravelHistoryMap })));
const WanderlustHeader = lazy(() => import('~/components/wanderlust/WanderlustHeader').then(module => ({ default: module.WanderlustHeader })));
const CityHub = lazy(() => import('~/components/wanderlust/CityHub').then(module => ({ default: module.CityHub })));
const ExplorerLog = lazy(() => import('~/components/wanderlust/ExplorerLog').then(module => ({ default: module.ExplorerLog })));
const SmartRouteCard = lazy(() => import('~/components/route-planner/SmartRouteCard').then(module => ({ default: module.SmartRouteCard })));
const RouteMap = lazy(() => import('~/components/route-planner/RouteMap').then(module => ({ default: module.RouteMap })));
const TurnByTurnDirections = lazy(() => import('~/components/route-planner/TurnByTurnDirections').then(module => ({ default: module.TurnByTurnDirections })));

interface MapExplorerProps {
  mode: 'venue-discovery' | 'travel-history' | 'route-planning';
  regions: any[]; // Add regions prop
}

export function MapExplorerComponent({ mode, regions }: MapExplorerProps) {
  // Remove useLoaderData as data is now passed as a prop
  // const { regions, success, error } = useLoaderData<typeof loadData>();

  const {
    loadRegionData,
    setError,
    setLoading,
  } = useWanderlustStore();

  // Load data into store on component mount
  useEffect(() => {
    // Check if regions prop is available and not empty
    if (regions && regions.length > 0) {
      setLoading(true);
      try {
        loadRegionData(regions);
        setError(null);
      } catch (err) {
        setError('Failed to initialize travel data');
        console.error('Error loading region data:', err);
      } finally {
        setLoading(false);
      }
    } else {
      // Handle case where regions prop is empty or null
      setError('No region data available');
    }
  }, [regions, loadRegionData, setError, setLoading]); // Depend on regions prop

  // Determine features based on mode
  const getFeaturesByMode = (mode: string): MapFeature[] => {
    switch (mode) {
      case 'route-planning':
        return ['markers', 'routes', 'traffic'] as MapFeature[];
      case 'venue-discovery':
        return ['markers', 'search'] as MapFeature[];
      case 'travel-history':
        return ['markers'] as MapFeature[];
      default:
        return ['markers'] as MapFeature[];
    }
  };

  return (
    <div className="h-full w-full relative">
      <MapProvider features={getFeaturesByMode(mode)}>
        {/* Map Content based on mode - each mode renders its own map component */}
        <Suspense fallback={<SkeletonLoader />}>
          {mode === 'venue-discovery' && (
            <>
              <VenueExplorerMap /> {/* Map-specific layers/features */}
              <VenueDiscoveryPanel /> {/* Overlay panel */}
            </>
          )}
          {mode === 'travel-history' && (
            <>
              <TravelHistoryMap /> {/* Map-specific layers/features for travel history */}
              {/* CityHub and ExplorerLog as a sidebar/overlay */}
              <div className="travel-history-sidebar"> {/* Add CSS for positioning */}
                <WanderlustHeader />
                <CityHub regions={regions} />
                <ExplorerLog />
              </div>
            </>
          )}
          {mode === 'route-planning' && (
            <>
              <RouteMap /> {/* Map-specific layers/features */}
              {/* SmartRouteCard and TurnByTurnDirections as a sidebar/overlay */}
              <div className="route-planning-sidebar"> {/* Add CSS for positioning */}
                <SmartRouteCard regions={regions} />
                <TurnByTurnDirections />
              </div>
            </>
          )}
        </Suspense>
      </MapProvider>
    </div>
  );
}