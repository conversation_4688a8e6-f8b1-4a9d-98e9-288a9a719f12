// Debug route to test RouteMap with sample data
import React, { useEffect } from 'react';
import { MapExplorerComponent } from '~/components/shared/MapExplorer';
import { useWanderlustStore } from '~/stores/wanderlust';
import type { VisitedPlace } from '~/types/wanderlust';

// Sample test places for Nashville area
const testPlaces: VisitedPlace[] = [
  {
    id: 'test-start',
    name: 'Nashville International Airport',
    category: 'transport',
    coordinates: { latitude: 36.1245, longitude: -86.6782 },
    description: {
      en: 'Starting point - Nashville International Airport',
      fr: 'Point de départ - Aéroport international de Nashville',
      ar: 'نقطة البداية - مطار ناشفيل الدولي'
    },
    city: 'Nashville',
    region: 'Tennessee',
    icon: '✈️',
    rating: 4.0,
    revisitPotential: 'Worth a Look',
    keyTakeaway: 'Airport starting point',
    personalNotes: 'Flight arrival point',
    visitDate: new Date().toISOString().split('T')[0]
  },
  {
    id: 'test-downtown',
    name: 'Downtown Nashville',
    category: 'entertainment',
    coordinates: { latitude: 36.1627, longitude: -86.7816 },
    description: {
      en: 'Heart of Nashville with music venues and attractions',
      fr: 'Cœur de Nashville avec des salles de musique et des attractions',
      ar: 'قلب ناشفيل مع أماكن الموسيقى والمعالم السياحية'
    },
    city: 'Nashville',
    region: 'Tennessee',
    icon: '🎵',
    rating: 4.8,
    revisitPotential: 'Must Return',
    keyTakeaway: 'Amazing music scene',
    personalNotes: 'Great live music venues',
    visitDate: new Date().toISOString().split('T')[0]
  },
  {
    id: 'test-hotel',
    name: 'The Hermitage Hotel',
    category: 'accommodation',
    coordinates: { latitude: 36.1612, longitude: -86.7775 },
    description: {
      en: 'Historic luxury hotel in downtown Nashville',
      fr: 'Hôtel de luxe historique au centre-ville de Nashville',
      ar: 'فندق تاريخي فاخر في وسط مدينة ناشفيل'
    },
    city: 'Nashville',
    region: 'Tennessee',
    icon: '🏨',
    rating: 4.5,
    revisitPotential: 'Worth a Look',
    keyTakeaway: 'Beautiful historic hotel',
    personalNotes: 'Excellent service and location',
    visitDate: new Date().toISOString().split('T')[0]
  }
];

export default function DebugRouteMap() {
  const { addToItinerary, clearItinerary, itinerary } = useWanderlustStore();

  // Add test places to itinerary on component mount
  useEffect(() => {
    console.log('🔧 Debug: Adding test places to itinerary');
    clearItinerary(); // Clear existing itinerary
    testPlaces.forEach(place => {
      addToItinerary(place);
    });
  }, [addToItinerary, clearItinerary]);

  return (
    <div className="h-screen flex flex-col">
      <div className="bg-black text-white p-4">
        <h1 className="text-2xl font-bold mb-2">🔧 Route Map Debug</h1>
        <p className="text-sm text-gray-300 mb-2">
          Testing RouteMap component with sample Nashville waypoints
        </p>
        <div className="text-xs text-gray-400">
          <p>Itinerary places: {itinerary.length}</p>
          <p>Test places: {testPlaces.map(p => p.name).join(', ')}</p>
        </div>
      </div>
      
      <div className="flex-1">
        <MapExplorerComponent 
          mode="route-planning" 
          regions={[]} // Empty regions for this test
        />
      </div>
      
      <div className="bg-gray-100 p-4 text-sm">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <ul className="space-y-1 text-xs">
          <li>• Check browser console for map loading messages</li>
          <li>• Look for "🗺️ BaseMapComponent [route-planning]: Places data" logs</li>
          <li>• Look for "🎯 MarkerLayer: Rendering X markers" logs</li>
          <li>• Verify Google Maps API is loaded</li>
          <li>• Check if waypoint markers appear on the map</li>
        </ul>
      </div>
    </div>
  );
}
