// Simple test route to debug Google Maps loading
import React from 'react';
import { GoogleMap } from '@react-google-maps/api';
import { useGoogleMaps } from '~/components/maps/GoogleMapsLoader';

const mapContainerStyle = {
  width: '100%',
  height: '400px'
};

const center = {
  lat: 36.1627,
  lng: -86.7816
};

export default function TestMap() {
  const { isLoaded, loadError } = useGoogleMaps();

  console.log('🔍 TestMap Debug:', {
    isLoaded,
    loadError: loadError?.message,
    windowGoogle: !!(window as any).google,
    windowGoogleMaps: !!(window as any).google?.maps,
  });

  if (loadError) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Google Maps Load Error</h1>
        <div className="bg-red-50 border border-red-200 rounded p-4">
          <p className="text-red-800">{loadError.message}</p>
        </div>
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Debug Info:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>API Key Present: {!!(import.meta.env.VITE_GOOGLE_MAPS_API_KEY)}</li>
            <li>API Key Length: {(import.meta.env.VITE_GOOGLE_MAPS_API_KEY)?.length || 0}</li>
            <li>Window Google: {!!(window as any).google ? 'Yes' : 'No'}</li>
            <li>Window Google Maps: {!!(window as any).google?.maps ? 'Yes' : 'No'}</li>
          </ul>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Loading Google Maps...</h1>
        <div className="animate-pulse bg-gray-200 h-96 rounded"></div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Google Maps Test</h1>
      <div className="border rounded overflow-hidden">
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={center}
          zoom={10}
          onLoad={(map) => {
            console.log('✅ Google Map loaded successfully:', map);
          }}
          onError={(error) => {
            console.error('❌ Google Map error:', error);
          }}
        >
          {/* Test marker */}
          {(window as any).google?.maps && (
            <div>Map loaded successfully!</div>
          )}
        </GoogleMap>
      </div>
      <div className="mt-4 text-sm text-gray-600">
        <p>If you see a map above, Google Maps is working correctly.</p>
        <p>Center: {center.lat}, {center.lng}</p>
        <p>Zoom: 10</p>
      </div>
    </div>
  );
}
