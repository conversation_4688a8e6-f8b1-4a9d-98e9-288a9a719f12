import { redirect } from "react-router";
import type { Route } from "./+types/emoji-catch-all";

export async function loader({ request }: Route.LoaderArgs) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // Check if the URL contains URL-encoded emoji characters
  const emojiPattern = /%F0%9F/;
  
  if (emojiPattern.test(pathname)) {
    console.log('🚫 Caught emoji navigation attempt:', pathname);
    // Redirect emoji URLs to the home page to prevent 404 errors
    throw redirect('/');
  }
  
  // For other unmatched routes, throw a 404
  throw new Response('Not Found', { status: 404 });
}

export default function EmojiCatchAll() {
  // This component should never render since we always redirect or throw 404
  return null;
}
