// Debug route to test Google Maps API loading and basic functionality
import React, { useEffect, useState } from 'react';

export default function DebugMapsApi() {
  const [apiStatus, setApiStatus] = useState({
    loaded: false,
    error: null as string | null,
    libraries: {
      maps: false,
      places: false,
      geometry: false,
      routes: false
    }
  });

  useEffect(() => {
    const checkGoogleMapsApi = () => {
      console.log('🔍 Checking Google Maps API status...');
      
      if (typeof window === 'undefined') {
        setApiStatus(prev => ({ ...prev, error: 'Window not available (SSR)' }));
        return;
      }

      if (!window.google) {
        setApiStatus(prev => ({ ...prev, error: 'window.google not available' }));
        return;
      }

      if (!window.google.maps) {
        setApiStatus(prev => ({ ...prev, error: 'window.google.maps not available' }));
        return;
      }

      // Check available libraries
      const libraries = {
        maps: !!window.google.maps,
        places: !!window.google.maps.places,
        geometry: !!window.google.maps.geometry,
        routes: !!(window.google.maps as any).routes
      };

      console.log('✅ Google Maps API Status:', {
        loaded: true,
        libraries,
        DirectionsService: !!window.google.maps.DirectionsService,
        LatLng: !!window.google.maps.LatLng,
        Map: !!window.google.maps.Map,
        Marker: !!window.google.maps.Marker,
        Polyline: !!window.google.maps.Polyline,
        geometry: {
          encoding: !!window.google.maps.geometry?.encoding,
          spherical: !!window.google.maps.geometry?.spherical
        }
      });

      setApiStatus({
        loaded: true,
        error: null,
        libraries
      });
    };

    // Check immediately
    checkGoogleMapsApi();

    // Also check after a delay in case the API is still loading
    const timeouts = [1000, 3000, 5000].map(delay => 
      setTimeout(checkGoogleMapsApi, delay)
    );

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, []);

  const testPolylineDecoding = () => {
    if (!window.google?.maps?.geometry?.encoding) {
      console.error('❌ Google Maps geometry encoding not available');
      return;
    }

    try {
      // Simple test polyline (Nashville area)
      const testPolyline = 'u~vdFfxzbOqBgAcAe@}@c@{@e@y@g@w@i@u@k@s@m@q@o@o@q@m@s@k@u@i@w@g@y@e@{@c@}@a@_Aa@aAa@cAa@eAa@gAa@iAa@kAa@mAa@oAa@qAa@sAa@uAa@wAa@yAa@{Aa@}Aa@_Ba@aBa@cBa@eBa@gBa@iBa@kBa@mBa@oBa@qBa@sBa@uBa@wBa@yBa@{Ba@}Ba@_Ca@aCa@cCa@eCa@gCa@iCa@kCa@mCa@oCa@qCa@sCa@uCa@wCa@yCa@{Ca@}Ca@_Da@aDa@cDa@eDa@gDa@iDa@kDa@mDa@oDa@qDa@sDa@uDa@wDa@yDa@{Da@}Da@_Ea@aEa@cEa@eEa@gEa@iEa@kEa@mEa@oEa@qEa@sEa@uEa@wEa@yEa@{Ea@}Ea@_Fa@aFa@cFa@eFa@gFa@iFa@kFa@mFa@oFa@qFa@sFa@uFa@wFa@yFa@{Fa@}Fa@_Ga@aGa@cGa@eGa@gGa@iGa@kGa@mGa@oGa@qGa@sGa@uGa@wGa@yGa@{Ga@}Ga@_Ha@aHa@cHa@eHa@gHa@iHa@kHa@mHa@oHa@qHa@sHa@uHa@wHa@yHa@{Ha@}Ha@_Ia@aIa@cIa@eIa@gIa@iIa@kIa@mIa@oIa@qIa@sIa@uIa@wIa@yIa@{Ia@}Ia@_Ja@aJa@cJa@eJa@gJa@iJa@kJa@mJa@oJa@qJa@sJa@uJa@wJa@yJa@{Ja@}Ja@_Ka@aKa@cKa@eKa@gKa@iKa@kKa@mKa@oKa@qKa@sKa@uKa@wKa@yKa@{Ka@}Ka@_La@aLa@cLa@eLa@gLa@iLa@kLa@mLa@oLa@qLa@sLa@uLa@wLa@yLa@{La@}La@_Ma@aMa@cMa@eMa@gMa@iMa@kMa@mMa@oMa@qMa@sMa@uMa@wMa@yMa@{Ma@}Ma@_Na@aNa@cNa@eNa@gNa@iNa@kNa@mNa@oNa@qNa@sNa@uNa@wNa@yNa@{Na@}Na@_Oa@aOa@cOa@eOa@gOa@iOa@kOa@mOa@oOa@qOa@sOa@uOa@wOa@yOa@{Oa@}Oa@_Pa@aPa@cPa@ePa@gPa@iPa@kPa@mPa@oPa@qPa@sPa@uPa@wPa@yPa@{Pa@}Pa@_Qa@aQa@cQa@eQa@gQa@iQa@kQa@mQa@oQa@qQa@sQa@uQa@wQa@yQa@{Qa@}Qa@_Ra@aRa@cRa@eRa@gRa@iRa@kRa@mRa@oRa@qRa@sRa@uRa@wRa@yRa@{Ra@}Ra@_Sa@aSa@cSa@eSa@gSa@iSa@kSa@mSa@oSa@qSa@sSa@uSa@wSa@ySa@{Sa@}Sa@_Ta@aTa@cTa@eTa@gTa@iTa@kTa@mTa@oTa@qTa@sTa@uTa@wTa@yTa@{Ta@}Ta@_Ua@aUa@cUa@eUa@gUa@iUa@kUa@mUa@oUa@qUa@sUa@uUa@wUa@yUa@{Ua@}Ua@_Va@aVa@cVa@eVa@gVa@iVa@kVa@mVa@oVa@qVa@sVa@uVa@wVa@yVa@{Va@}Va@_Wa@aWa@cWa@eWa@gWa@iWa@kWa@mWa@oWa@qWa@sWa@uWa@wWa@yWa@{Wa@}Wa@_Xa@aXa@cXa@eXa@gXa@iXa@kXa@mXa@oXa@qXa@sXa@uXa@wXa@yXa@{Xa@}Xa@_Ya@aYa@cYa@eYa@gYa@iYa@kYa@mYa@oYa@qYa@sYa@uYa@wYa@yYa@{Ya@}Ya@_Za@aZa@cZa@eZa@gZa@iZa@kZa@mZa@oZa@qZa@sZa@uZa@wZa@yZa@{Za@}Za@_[a@a[a@c[a@e[a@g[a@i[a@k[a@m[a@o[a@q[a@s[a@u[a@w[a@y[a@{[a@}[a@_\\a@a\\a@c\\a@e\\a@g\\a@i\\a@k\\a@m\\a@o\\a@q\\a@s\\a@u\\a@w\\a@y\\a@{\\a@}\\a@_]a@a]a@c]a@e]a@g]a@i]a@k]a@m]a@o]a@q]a@s]a@u]a@w]a@y]a@{]a@}]a@_^a@a^a@c^a@e^a@g^a@i^a@k^a@m^a@o^a@q^a@s^a@u^a@w^a@y^a@{^a@}^a@__a@a_a@c_a@e_a@g_a@i_a@k_a@m_a@o_a@q_a@s_a@u_a@w_a@y_a@{_a@}_a@_`a@a`a@c`a@e`a@g`a@i`a@k`a@m`a@o`a@q`a@s`a@u`a@w`a@y`a@{`a@}`a@_aa@aaa@caa@eaa@gaa@iaa@kaa@maa@oaa@qaa@saa@uaa@waa@yaa@{aa@}aa@_ba@aba@cba@eba@gba@iba@kba@mba@oba@qba@sba@uba@wba@yba@{ba@}ba@_ca@aca@cca@eca@gca@ica@kca@mca@oca@qca@sca@uca@wca@yca@{ca@}ca@_da@ada@cda@eda@gda@ida@kda@mda@oda@qda@sda@uda@wda@yda@{da@}da@_ea@aea@cea@eea@gea@iea@kea@mea@oea@qea@sea@uea@wea@yea@{ea@}ea@_fa@afa@cfa@efa@gfa@ifa@kfa@mfa@ofa@qfa@sfa@ufa@wfa@yfa@{fa@}fa@_ga@aga@cga@ega@gga@iga@kga@mga@oga@qga@sga@uga@wga@yga@{ga@}ga@_ha@aha@cha@eha@gha@iha@kha@mha@oha@qha@sha@uha@wha@yha@{ha@}ha@_ia@aia@cia@eia@gia@iia@kia@mia@oia@qia@sia@uia@wia@yia@{ia@}ia@_ja@aja@cja@eja@gja@ija@kja@mja@oja@qja@sja@uja@wja@yja@{ja@}ja@_ka@aka@cka@eka@gka@ika@kka@mka@oka@qka@ska@uka@wka@yka@{ka@}ka@_la@ala@cla@ela@gla@ila@kla@mla@ola@qla@sla@ula@wla@yla@{la@}la@_ma@ama@cma@ema@gma@ima@kma@mma@oma@qma@sma@uma@wma@yma@{ma@}ma@_na@ana@cna@ena@gna@ina@kna@mna@ona@qna@sna@una@wna@yna@{na@}na@_oa@aoa@coa@eoa@goa@ioa@koa@moa@ooa@qoa@soa@uoa@woa@yoa@{oa@}oa@_pa@apa@cpa@epa@gpa@ipa@kpa@mpa@opa@qpa@spa@upa@wpa@ypa@{pa@}pa@_qa@aqa@cqa@eqa@gqa@iqa@kqa@mqa@oqa@qqa@sqa@uqa@wqa@yqa@{qa@}qa@_ra@ara@cra@era@gra@ira@kra@mra@ora@qra@sra@ura@wra@yra@{ra@}ra@_sa@asa@csa@esa@gsa@isa@ksa@msa@osa@qsa@ssa@usa@wsa@ysa@{sa@}sa@_ta@ata@cta@eta@gta@ita@kta@mta@ota@qta@sta@uta@wta@yta@{ta@}ta@_ua@aua@cua@eua@gua@iua@kua@mua@oua@qua@sua@uua@wua@yua@{ua@}ua@_va@ava@cva@eva@gva@iva@kva@mva@ova@qva@sva@uva@wva@yva@{va@}va@_wa@awa@cwa@ewa@gwa@iwa@kwa@mwa@owa@qwa@swa@uwa@wwa@ywa@{wa@}wa@_xa@axa@cxa@exa@gxa@ixa@kxa@mxa@oxa@qxa@sxa@uxa@wxa@yxa@{xa@}xa@_ya@aya@cya@eya@gya@iya@kya@mya@oya@qya@sya@uya@wya@yya@{ya@}ya@_za@aza@cza@eza@gza@iza@kza@mza@oza@qza@sza@uza@wza@yza@{za@}za@_{a@a{a@c{a@e{a@g{a@i{a@k{a@m{a@o{a@q{a@s{a@u{a@w{a@y{a@{{a@}{a@_|a@a|a@c|a@e|a@g|a@i|a@k|a@m|a@o|a@q|a@s|a@u|a@w|a@y|a@{|a@}|a@_}a@a}a@c}a@e}a@g}a@i}a@k}a@m}a@o}a@q}a@s}a@u}a@w}a@y}a@{}a@}}a@_~a@a~a@c~a@e~a@g~a@i~a@k~a@m~a@o~a@q~a@s~a@u~a@w~a@y~a@{~a@}~a@_a@aa@ca@ea@ga@ia@ka@ma@oa@qa@sa@ua@wa@ya@{a@}a@';
      
      const decodedPath = window.google.maps.geometry.encoding.decodePath(testPolyline);
      console.log('✅ Polyline decoding test successful:', {
        originalLength: testPolyline.length,
        decodedPoints: decodedPath.length,
        firstPoint: decodedPath[0] ? { lat: decodedPath[0].lat(), lng: decodedPath[0].lng() } : null,
        lastPoint: decodedPath[decodedPath.length - 1] ? { 
          lat: decodedPath[decodedPath.length - 1].lat(), 
          lng: decodedPath[decodedPath.length - 1].lng() 
        } : null
      });
      
      return true;
    } catch (error) {
      console.error('❌ Polyline decoding test failed:', error);
      return false;
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      <div className="bg-black text-white p-6">
        <h1 className="text-2xl font-bold mb-2">🔍 Google Maps API Debug</h1>
        <p className="text-sm text-gray-300">
          Testing Google Maps API loading and functionality
        </p>
      </div>
      
      <div className="flex-1 p-6 overflow-auto">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* API Status */}
          <div className="bg-white rounded-lg p-6 shadow">
            <h2 className="text-lg font-semibold mb-4">API Status</h2>
            <div className="space-y-2">
              <div className={`flex items-center gap-2 ${apiStatus.loaded ? 'text-green-600' : 'text-red-600'}`}>
                <span className="text-lg">{apiStatus.loaded ? '✅' : '❌'}</span>
                <span>Google Maps API: {apiStatus.loaded ? 'Loaded' : 'Not Loaded'}</span>
              </div>
              {apiStatus.error && (
                <div className="text-red-600 text-sm">
                  Error: {apiStatus.error}
                </div>
              )}
            </div>
          </div>

          {/* Libraries Status */}
          <div className="bg-white rounded-lg p-6 shadow">
            <h2 className="text-lg font-semibold mb-4">Libraries Status</h2>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(apiStatus.libraries).map(([lib, available]) => (
                <div key={lib} className={`flex items-center gap-2 ${available ? 'text-green-600' : 'text-red-600'}`}>
                  <span className="text-lg">{available ? '✅' : '❌'}</span>
                  <span>{lib}: {available ? 'Available' : 'Not Available'}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Environment Variables */}
          <div className="bg-white rounded-lg p-6 shadow">
            <h2 className="text-lg font-semibold mb-4">Environment Variables</h2>
            <div className="space-y-2 text-sm">
              <div>
                <strong>API Key:</strong> {import.meta.env.VITE_GOOGLE_MAPS_API_KEY ? 
                  `${import.meta.env.VITE_GOOGLE_MAPS_API_KEY.substring(0, 10)}...` : 
                  'Not Set'
                }
              </div>
              <div>
                <strong>Map ID:</strong> {import.meta.env.VITE_GOOGLE_MAPS_MAP_ID || 'Not Set'}
              </div>
            </div>
          </div>

          {/* Test Functions */}
          <div className="bg-white rounded-lg p-6 shadow">
            <h2 className="text-lg font-semibold mb-4">Test Functions</h2>
            <div className="space-y-4">
              <button
                onClick={testPolylineDecoding}
                disabled={!apiStatus.libraries.geometry}
                className="px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                Test Polyline Decoding
              </button>
              <p className="text-sm text-gray-600">
                Tests if Google Maps geometry encoding can decode polylines (required for route visualization)
              </p>
            </div>
          </div>

          {/* Console Instructions */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-2 text-yellow-800">Console Instructions</h2>
            <p className="text-sm text-yellow-700 mb-2">
              Open browser console (F12) to see detailed debug information.
            </p>
            <ul className="text-xs text-yellow-600 space-y-1">
              <li>• Look for "🔍 Checking Google Maps API status..." messages</li>
              <li>• Check for "✅ Google Maps API Status:" with library details</li>
              <li>• Test polyline decoding to verify geometry library</li>
              <li>• Any errors will be logged with ❌ prefix</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
