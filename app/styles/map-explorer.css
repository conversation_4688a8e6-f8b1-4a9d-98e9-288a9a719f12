.map-explorer-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* Or appropriate height */
}

.mode-buttons-container {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.mode-button {
  display: flex; /* Align icon and text */
  align-items: center;
  gap: 0.5rem; /* Space between icon and text */
  padding: 0.5rem 1rem;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
  border-radius: 4px; /* Example styling */
}

.active-mode {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
  font-weight: bold;
}

.button-icon {
  width: 1.2em; /* Example icon size */
  height: 1.2em;
}

.map-container {
  flex-grow: 1;
  width: 100%;
  position: relative; /* Needed if loading/empty states overlay */
}

.error-banner, .loading-spinner, .empty-state {
  padding: 1rem;
  margin: 1rem;
  text-align: center;
  border-radius: 4px;
}

.error-banner {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.loading-spinner {
  background-color: #e9ecef;
  color: #495057;
}

.empty-state {
  background-color: #fff3cd;
  color: #856404;
    border: 1px solid #ffeeba;
  }

  /* Styles for mode-specific sidebars/overlays */
  .travel-history-sidebar,
  .route-planning-sidebar {
    position: absolute; /* Position over the map */
    top: 1rem; /* Adjust as needed */
    left: 1rem; /* Adjust as needed */
    z-index: 40; /* Higher than SearchOverlay (z-30) but below modals */
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent background */
    padding: 1rem;
    border-radius: 8px;
    max-height: calc(100% - 2rem); /* Limit height to prevent overflow */
    overflow-y: auto; /* Enable scrolling if content exceeds max height */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 300px; /* Example width, adjust as needed */
  }

  /* Z-index hierarchy for map components */
  .fifa-search-overlay {
    z-index: 30; /* Below route planning sidebar */
  }

  .fifa-search-results {
    z-index: 31; /* Slightly above search overlay */
  }

  /* Optional: Adjust position for larger screens if needed */
  @media (min-width: 768px) {
    .travel-history-sidebar,
    .route-planning-sidebar {
      /* Example: position on the right */
      left: auto;
      right: 1rem;
    }
  }