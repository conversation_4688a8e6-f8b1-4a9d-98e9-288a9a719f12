@import "tailwindcss";
@import "tw-animate-css";
@import "./styles/theme.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Global Base Styles - Brand Colors Dark Theme */
html,
body {
  @apply bg-gradient-to-br from-black via-gray-900 to-black text-white;
  min-height: 100vh;
  font-family: var(--font-sans);

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

/* Smooth scrolling and improved performance */
html {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: #f8f9fa;
  --sidebar-foreground: #212529;
  --sidebar-primary: #007bff;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #28a745;
  --sidebar-accent-foreground: #fff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(255, 215, 0, 0.25);
}

:root {
  --radius: 0.625rem;

  /* Brand Color System - Gold, Red, Black */
  --background: #000000; /* Pure black */
  --foreground: #ffffff;
  --card: rgba(255, 255, 255, 0.1); /* glassmorphism */
  --card-foreground: #ffffff;
  --popover: rgba(255, 255, 255, 0.1);
  --popover-foreground: #ffffff;
  --primary: #FFD700; /* Brand gold */
  --primary-foreground: #000000;
  --secondary: #DC2626; /* Brand red */
  --secondary-foreground: #ffffff;
  --muted: rgba(255, 255, 255, 0.05);
  --muted-foreground: rgba(255, 255, 255, 0.7);
  --accent: #FFD700; /* Brand gold */
  --accent-foreground: #000000;
  --destructive: #DC2626; /* Brand red */
  --border: rgba(255, 255, 255, 0.2);
  --input: rgba(255, 255, 255, 0.15);
  --ring: rgba(255, 215, 0, 0.25); /* Gold with opacity */

  /* Brand Background Gradients */
  --bg-primary: linear-gradient(135deg, #000000 0%, #1f1f1f 50%, #000000 100%); /* Black gradient */
  --bg-secondary: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%); /* Dark gray gradient */
  --bg-hero: linear-gradient(135deg, #000000 0%, #DC2626 25%, #000000 50%, #FFD700 75%, #000000 100%); /* Brand colors */

  /* Brand Color Palette */
  --color-gold: #FFD700; /* Primary brand gold */
  --color-gold-dark: #B8860B; /* Darker gold for hover states */
  --color-gold-light: #FFF8DC; /* Light gold for subtle accents */
  --color-red: #DC2626; /* Primary brand red */
  --color-red-dark: #991B1B; /* Darker red for hover states */
  --color-red-light: #FEE2E2; /* Light red for subtle accents */
  --color-black: #000000; /* Primary brand black */
  --color-gray-dark: #1a1a1a; /* Dark gray */
  --color-gray: #2a2a2a; /* Medium gray */
  --color-gray-light: #404040; /* Light gray */

  /* Complementary Colors (for variety while maintaining brand focus) */
  --color-green: #16A34A; /* Success green */
  --color-blue: #2563EB; /* Info blue */
  --color-cyan: #0891B2; /* Accent cyan */
  --color-orange: #EA580C; /* Warning orange */

  /* Activity Type Colors - Brand Style */
  --color-transport: var(--color-blue);
  --color-match: var(--color-red); /* Brand red for matches */
  --color-meal: var(--color-gold); /* Brand gold for meals */
  --color-hotel: var(--color-cyan);
  --color-activity: var(--color-gold); /* Brand gold for activities */
  --color-default: rgba(255, 255, 255, 0.6);
  --color-weather-icon: var(--color-gold); /* Brand gold for weather */

  /* Layout & Container System */
  --container-max-width: 1280px; /* max-w-7xl */
  --container-padding-x: 1.5rem; /* px-6 */
  --container-padding-x-sm: 1rem; /* px-4 */
  --container-padding-x-lg: 2rem; /* px-8 */

  /* Spacing Scale - Dashboard Inspired */
  --spacing-xs: 0.25rem; /* 1 */
  --spacing-sm: 0.5rem; /* 2 */
  --spacing-md: 1rem; /* 4 */
  --spacing-lg: 1.5rem; /* 6 */
  --spacing-xl: 2rem; /* 8 */
  --spacing-2xl: 3rem; /* 12 */
  --spacing-3xl: 4rem; /* 16 */
  --spacing-4xl: 6rem; /* 24 */
  --spacing-5xl: 8rem; /* 32 */

  /* Section Spacing */
  --section-padding-y: 4rem; /* py-16 */
  --section-padding-y-lg: 6rem; /* py-24 */
  --section-margin-y: 3rem; /* my-12 */
  --section-margin-y-lg: 4rem; /* my-16 */

  /* Animation & Transitions - Dashboard Style */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Brand Gradient System */
  --gradient-primary: linear-gradient(135deg, var(--color-gold) 0%, var(--color-gold-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-red) 0%, var(--color-red-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-gold) 0%, var(--color-red) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-green) 0%, var(--color-cyan) 100%);
  --gradient-hero: linear-gradient(135deg, #000000 0%, #DC2626 25%, #000000 50%, #FFD700 75%, #000000 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  --gradient-card-hover: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-hover: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-backdrop: blur(16px);
}

/* Enhanced Dark Mode - Brand Consistency */
.dark {
  /* Inherit the same brand colors */
  --background: #000000;
  --foreground: #ffffff;
  --card: var(--glass-bg);
  --card-foreground: #ffffff;
  --popover: var(--glass-bg);
  --popover-foreground: #ffffff;
  --primary: var(--color-gold);
  --primary-foreground: #000000;
  --secondary: var(--color-red);
  --secondary-foreground: #ffffff;
  --muted: var(--glass-bg);
  --muted-foreground: rgba(255, 255, 255, 0.7);
  --accent: var(--color-gold);
  --accent-foreground: #000000;
  --destructive: var(--color-red);
  --border: var(--glass-border);
  --input: rgba(255, 255, 255, 0.15);
  --ring: rgba(255, 215, 0, 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-gradient-to-br from-black via-gray-900 to-black text-white;
    font-family: var(--font-sans);
    line-height: 1.6;
  }

  /* Typography Base */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold text-white;
    line-height: 1.2;
  }

  h1 { @apply text-4xl md:text-6xl; }
  h2 { @apply text-3xl md:text-4xl; }
  h3 { @apply text-2xl md:text-3xl; }
  h4 { @apply text-xl md:text-2xl; }
  h5 { @apply text-lg md:text-xl; }
  h6 { @apply text-base md:text-lg; }

  p {
    @apply text-white/80 leading-relaxed;
  }

  a {
    @apply text-white hover:text-white/80 transition-colors duration-300;
  }

  /* Prevent emojis from being treated as clickable elements */
  .emoji-safe {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* Global emoji protection - prevent any emoji from being treated as a link */
  span[class*="emoji"],
  span:has(emoji),
  *:contains("🍕"),
  *:contains("🎭"),
  *:contains("🏛️"),
  *:contains("🚇"),
  *:contains("🌳"),
  *:contains("🎨"),
  *:contains("🛍️"),
  *:contains("🏨"),
  *:contains("🍽️") {
    pointer-events: none !important;
    user-select: none !important;
    text-decoration: none !important;
  }

  /* Prevent browser extensions from converting emojis to links */
  body *[href*="%F0%9F"],
  body a[href*="%F0%9F"] {
    pointer-events: none !important;
    display: none !important;
  }
}

@layer components {
  /* Container System */
  .container-custom {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding-left: var(--container-padding-x);
    padding-right: var(--container-padding-x);
  }

  @media (min-width: 640px) {
    .container-custom {
      padding-left: var(--container-padding-x-sm);
      padding-right: var(--container-padding-x-sm);
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: var(--container-padding-x-lg);
      padding-right: var(--container-padding-x-lg);
    }
  }

  /* Glassmorphism Components - Dashboard Style */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
  }

  .glass-card:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .glass-button {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    transition: all var(--transition-normal);
    cursor: pointer;
  }

  .glass-button:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  /* Activity Type Components */
  .text-transport { color: var(--color-transport); }
  .text-match { color: var(--color-match); }
  .text-meal { color: var(--color-meal); }
  .text-hotel { color: var(--color-hotel); }
  .text-activity { color: var(--color-activity); }
  .text-default { color: var(--color-default); }
  .text-weather-icon { color: var(--color-weather-icon); }

  /* Grid System */
  .grid-responsive {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  @media (min-width: 640px) {
    .grid-responsive { grid-template-columns: repeat(2, 1fr); }
  }

  @media (min-width: 1024px) {
    .grid-responsive { grid-template-columns: repeat(3, 1fr); }
  }

  @media (min-width: 1280px) {
    .grid-responsive { grid-template-columns: repeat(4, 1fr); }
  }
}

@layer utilities {
  /* Layout Utilities */
  .section-padding { padding: var(--section-padding-y) 0; }
  .section-padding-lg { padding: var(--section-padding-y-lg) 0; }
  .section-margin { margin: var(--section-margin-y) 0; }
  .section-margin-lg { margin: var(--section-margin-y-lg) 0; }

  /* Container Utilities */
  .px-container {
    padding-left: var(--container-padding-x);
    padding-right: var(--container-padding-x);
  }

  /* Gradient Utilities */
  .bg-gradient-primary { background: var(--gradient-primary); }
  .bg-gradient-secondary { background: var(--gradient-secondary); }
  .bg-gradient-accent { background: var(--gradient-accent); }
  .bg-gradient-success { background: var(--gradient-success); }
  .bg-gradient-hero { background: var(--gradient-hero); }
  .bg-gradient-card { background: var(--gradient-card); }
  .bg-gradient-card-hover { background: var(--gradient-card-hover); }

  /* Transition Utilities */
  .transition-fast { transition: all var(--transition-fast); }
  .transition-normal { transition: all var(--transition-normal); }
  .transition-slow { transition: all var(--transition-slow); }
  .transition-bounce { transition: all var(--transition-bounce); }

  /* Activity Type Utilities */
  .bg-transport { background-color: var(--color-transport); }
  .bg-match { background-color: var(--color-match); }
  .bg-meal { background-color: var(--color-meal); }
  .bg-hotel { background-color: var(--color-hotel); }
  .bg-activity { background-color: var(--color-activity); }
  .bg-default { background-color: var(--color-default); }

  .border-transport { border-color: var(--color-transport); }
  .border-match { border-color: var(--color-match); }
  .border-meal { border-color: var(--color-meal); }
  .border-hotel { border-color: var(--color-hotel); }
  .border-activity { border-color: var(--color-activity); }
  .border-default { border-color: var(--color-default); }

  /* Dashboard-specific Utilities */
  .bg-dashboard { @apply bg-gradient-to-br from-black via-gray-900 to-black; }
  .text-dashboard-primary { color: var(--color-gold); }
  .text-dashboard-secondary { color: var(--color-red); }
  .text-dashboard-accent { color: var(--color-gold); }

  /* Brand-specific Utilities */
  .text-brand-gold { color: var(--color-gold); }
  .text-brand-red { color: var(--color-red); }
  .text-brand-black { color: var(--color-black); }
  .bg-brand-gold { background-color: var(--color-gold); }
  .bg-brand-red { background-color: var(--color-red); }
  .bg-brand-black { background-color: var(--color-black); }
  .border-brand-gold { border-color: var(--color-gold); }
  .border-brand-red { border-color: var(--color-red); }
  .border-brand-black { border-color: var(--color-black); }
}

/* Custom Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    filter: brightness(1) drop-shadow(0 0 8px #FFD700);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2) drop-shadow(0 0 16px #FFD700);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
  }
}

@keyframes pulse-glow-red {
  0%, 100% { box-shadow: 0 0 20px rgba(220, 38, 38, 0.3); }
  50% { box-shadow: 0 0 30px rgba(220, 38, 38, 0.6); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-pulse-glow-red {
  animation: pulse-glow-red 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* ========================================
   DASHBOARD COMPONENT STYLES
   ======================================== */

/* Hero Title Gradient Text with Fallback */
.hero-title {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 25%, #FFD700 50%, #DC2626 75%, #FFD700 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: gradient-shift 4s ease infinite;
}

/* Fallback for browsers that don't support background-clip: text */
@supports not (-webkit-background-clip: text) {
  .hero-title {
    background: none;
    color: #FFD700;
    text-shadow:
      0 0 10px rgba(255, 215, 0, 0.5),
      0 0 20px rgba(220, 38, 38, 0.3),
      0 0 30px rgba(255, 215, 0, 0.2);
  }
}

/* Footer Title Gradient Text */
.footer-title {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 50%, #FFD700 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Fallback for footer title */
@supports not (-webkit-background-clip: text) {
  .footer-title {
    background: none;
    color: #FFD700;
    text-shadow:
      0 0 8px rgba(255, 215, 0, 0.4),
      0 0 16px rgba(220, 38, 38, 0.2);
  }
}

/* Action Button Styles with Hardcoded Colors */
.action-button-gold {
  background: linear-gradient(135deg, #FFD700 0%, #FFD700 30%, #DC2626 100%);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.action-button-gold:hover {
  background: linear-gradient(135deg, #FFF700 0%, #FFE55C 30%, #EF4444 100%);
  background-size: 200% 200%;
}

.action-button-red {
  background: linear-gradient(135deg, #DC2626 0%, #DC2626 30%, #FFD700 100%);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.action-button-red:hover {
  background: linear-gradient(135deg, #EF4444 0%, #F87171 30%, #FFF700 100%);
  background-size: 200% 200%;
}

/* Button Glow Effects */
.button-glow-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 50%, #FFD700 100%);
}

.button-glow-gold-inner {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.4) 0%, rgba(220, 38, 38, 0.4) 100%);
}

.button-glow-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 50%, #DC2626 100%);
}

.button-glow-red-inner {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.4) 0%, rgba(255, 215, 0, 0.4) 100%);
}

/* Footer Line Effects */
.footer-line-left {
  background: linear-gradient(to right, transparent, #FFD700);
}

.footer-line-right {
  background: linear-gradient(to right, #FFD700, transparent);
}

/* Loading State Styles */
.loading-particle {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
}

.loading-spinner {
  border-color: #FFD700;
}

.loading-icon {
  color: #FFD700;
}

.loading-title {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@supports not (-webkit-background-clip: text) {
  .loading-title {
    background: none;
    color: #FFD700;
  }
}

.loading-progress {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

/* Error State Styles */
.error-icon {
  color: #DC2626;
}

.error-button {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.error-button:hover {
  background: linear-gradient(135deg, #FFF700 0%, #EF4444 100%);
}

/* Premium Badge Styles */
.premium-badge-glow {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(220, 38, 38, 0.3) 50%, rgba(255, 215, 0, 0.3) 100%);
}

.premium-badge-bg {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);
}

.premium-badge-border {
  border-color: rgba(255, 215, 0, 0.4);
}

.premium-icon {
  color: #FFD700;
}

.premium-icon-glow {
  background: rgba(255, 215, 0, 0.3);
}

.premium-icon-glow-outer {
  background: rgba(255, 215, 0, 0.1);
}

.premium-subtitle {
  color: rgba(255, 215, 0, 0.8);
}

.live-indicator {
  background: #FFD700;
}

.live-indicator-glow {
  background: rgba(255, 215, 0, 0.5);
}

.live-data-badge {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
  color: #FFD700;
}

.live-data-border {
  border-color: rgba(255, 215, 0, 0.5);
}

/* Hero Title Background and Decorative Elements */
.hero-title-bg {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%, rgba(220, 38, 38, 0.1) 100%);
}

.hero-line-left {
  background: linear-gradient(to right, transparent, #FFD700);
}

.hero-line-right {
  background: linear-gradient(to right, #DC2626, transparent);
}

.hero-center-icon {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

/* Trip Dates Styles */
.trip-dates-glow {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
}

.trip-dates-icon {
  color: #FFD700;
}

.trip-dates-icon-glow {
  background: rgba(255, 215, 0, 0.2);
}

/* Stats Cards Styles */
.stats-card-glow-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 50%, #FFD700 100%);
}

.stats-card-glow-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 50%, #DC2626 100%);
}

.stats-card-border-gold:hover {
  border-color: rgba(255, 215, 0, 0.6);
}

.stats-card-border-red:hover {
  border-color: rgba(220, 38, 38, 0.6);
}

.stats-card-bg-gold {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, transparent 100%);
}

.stats-card-bg-red {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, transparent 100%);
}

.stats-icon-glow-gold {
  background: rgba(255, 215, 0, 0.2);
}

.stats-icon-glow-red {
  background: rgba(220, 38, 38, 0.2);
}

.stats-icon-bg-gold {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
}

.stats-icon-bg-red {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.2) 0%, rgba(255, 215, 0, 0.2) 100%);
}

.stats-icon-border-gold {
  border-color: rgba(255, 215, 0, 0.3);
}

.stats-icon-border-red {
  border-color: rgba(220, 38, 38, 0.3);
}

.stats-icon-gold {
  color: #FFD700;
}

.stats-icon-red {
  color: #DC2626;
}

.stats-number-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.stats-number-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

@supports not (-webkit-background-clip: text) {
  .stats-number-gold {
    background: none;
    color: #FFD700;
  }
  .stats-number-red {
    background: none;
    color: #DC2626;
  }
}

.stats-progress-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 50%, #FFD700 100%);
}

.stats-progress-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 50%, #DC2626 100%);
}

/* Additional Stats Card Elements */
.traveler-dot {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
}

.traveler-dot-glow {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.3) 0%, rgba(255, 215, 0, 0.3) 100%);
}

.destination-nav-icon {
  color: #FFD700;
}

.destination-nav-glow {
  background: rgba(255, 215, 0, 0.2);
}

.weather-orb {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.2) 0%, rgba(255, 215, 0, 0.2) 100%);
}

.weather-star {
  color: #FFD700;
}

/* Countdown Timer Styles */
.countdown-bg {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(220, 38, 38, 0.2) 50%, rgba(255, 215, 0, 0.2) 100%);
}

.countdown-particle {
  background: rgba(255, 215, 0, 0.3);
}

.countdown-icon {
  color: #FFD700;
}

.countdown-icon-glow {
  background: rgba(255, 215, 0, 0.2);
}

.countdown-title {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: gradient-shift 4s ease infinite;
}

@supports not (-webkit-background-clip: text) {
  .countdown-title {
    background: none;
    color: #FFD700;
  }
}

.countdown-glow-countdown-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.countdown-glow-countdown-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
}

.countdown-number-countdown-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.countdown-number-countdown-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

@supports not (-webkit-background-clip: text) {
  .countdown-number-countdown-gold {
    background: none;
    color: #FFD700;
  }
  .countdown-number-countdown-red {
    background: none;
    color: #DC2626;
  }
}

.countdown-progress {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

/* Tab Navigation Styles */
.tab-nav-glow {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(220, 38, 38, 0.3) 100%);
}

.tab-active {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

/* Tab Content Styles */
.tab-content-icon-gold {
  color: #FFD700;
}

.tab-content-icon-red {
  color: #DC2626;
}

.tab-progress-bar {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.itinerary-day-number {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.match-day-badge {
  background: rgba(220, 38, 38, 0.2);
  color: #DC2626;
}

/* Essential Categories Styles */
.essential-icon-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.essential-icon-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
}

.essential-category-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
}

.essential-category-red {
  background: linear-gradient(135deg, #DC2626 0%, #FFD700 100%);
}

.essential-check-icon {
  color: #FFD700;
}

/* Footer Styles */
.footer-bg-primary {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(220, 38, 38, 0.15) 50%, rgba(255, 215, 0, 0.15) 100%);
}

.footer-bg-secondary {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%, rgba(220, 38, 38, 0.1) 100%);
}

.footer-particle {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(220, 38, 38, 0.3) 100%);
}

.footer-heart-glow {
  background: rgba(220, 38, 38, 0.2);
}

.footer-heart-bg {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.2) 0%, rgba(255, 215, 0, 0.2) 100%);
}

.footer-heart-border {
  border-color: rgba(220, 38, 38, 0.4);
}

.footer-heart-icon {
  color: #DC2626;
}

.footer-action-dot {
  background: #FFD700;
}

.footer-action-text {
  color: rgba(255, 215, 0, 0.8);
}

.footer-sparkles {
  color: #FFD700;
}

.footer-premium-text {
  color: #FFD700;
}

/* Enhanced glassmorphism utilities */
.glass-premium {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-card-hover:hover {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(24px) saturate(200%);
  border: 1px solid rgba(255, 215, 0, 0.3);
  transform: translateY(-4px) scale(1.02);
}

/* Custom rounded corners for premium look */
.rounded-4xl {
  border-radius: 2rem;
}

/* Enhanced shadow utilities */
.shadow-premium {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.shadow-glow-gold {
  box-shadow:
    0 0 20px rgba(255, 215, 0, 0.3),
    0 0 40px rgba(255, 215, 0, 0.1),
    0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.shadow-glow-red {
  box-shadow:
    0 0 20px rgba(220, 38, 38, 0.3),
    0 0 40px rgba(220, 38, 38, 0.1),
    0 25px 50px -12px rgba(0, 0, 0, 0.5);
}
