/**
 * Map Loading Debug <PERSON>ript
 * 
 * Run this in the browser console to diagnose why the map isn't showing
 */

console.log('🔍 Starting Map Loading Diagnostic...');

// 1. Check Environment Variables
console.group('📋 Environment Configuration');
console.log('API Key Present:', !!import.meta?.env?.VITE_GOOGLE_MAPS_API_KEY);
console.log('API Key Length:', import.meta?.env?.VITE_GOOGLE_MAPS_API_KEY?.length || 0);
console.log('Map ID Present:', !!import.meta?.env?.VITE_GOOGLE_MAPS_MAP_ID);
console.groupEnd();

// 2. Check Google Maps Loading
console.group('🗺️ Google Maps API Status');
console.log('Window Google exists:', !!window.google);
console.log('Window Google Maps exists:', !!window.google?.maps);
console.log('Google Maps Geometry exists:', !!window.google?.maps?.geometry);
console.log('Google Maps Places exists:', !!window.google?.maps?.places);
console.groupEnd();

// 3. Check React Google Maps API
console.group('⚛️ React Google Maps API');
try {
  const reactGoogleMaps = require('@react-google-maps/api');
  console.log('React Google Maps API loaded:', !!reactGoogleMaps);
  console.log('GoogleMap component:', !!reactGoogleMaps.GoogleMap);
} catch (error) {
  console.log('React Google Maps API error:', error.message);
}
console.groupEnd();

// 4. Check DOM Elements
console.group('🏗️ DOM Elements');
const mapContainers = document.querySelectorAll('[data-testid="google-map"], .google-map, [class*="map"]');
console.log('Map containers found:', mapContainers.length);
mapContainers.forEach((container, index) => {
  console.log(`Container ${index}:`, {
    element: container.tagName,
    classes: container.className,
    id: container.id,
    style: container.style.cssText,
    dimensions: {
      width: container.offsetWidth,
      height: container.offsetHeight
    }
  });
});
console.groupEnd();

// 5. Check for Error Messages
console.group('❌ Error Detection');
const errorElements = document.querySelectorAll('[class*="error"], [data-testid*="error"]');
console.log('Error elements found:', errorElements.length);
errorElements.forEach((element, index) => {
  console.log(`Error ${index}:`, element.textContent);
});
console.groupEnd();

// 6. Check Network Requests
console.group('🌐 Network Status');
if (navigator.onLine) {
  console.log('✅ Browser is online');
} else {
  console.log('❌ Browser is offline');
}

// Check for Google Maps script tags
const scripts = document.querySelectorAll('script[src*="maps.googleapis.com"]');
console.log('Google Maps scripts found:', scripts.length);
scripts.forEach((script, index) => {
  console.log(`Script ${index}:`, script.src);
});
console.groupEnd();

// 7. Check React Components
console.group('⚛️ React Component Status');
const reactRoot = document.getElementById('root');
if (reactRoot) {
  console.log('React root found:', !!reactRoot);
  console.log('React root has children:', reactRoot.children.length > 0);
  
  // Look for map-related components
  const mapComponents = reactRoot.querySelectorAll('[class*="map"], [data-testid*="map"]');
  console.log('Map-related components:', mapComponents.length);
} else {
  console.log('❌ React root not found');
}
console.groupEnd();

// 8. Test Google Maps API Key
console.group('🔑 API Key Validation');
const apiKey = import.meta?.env?.VITE_GOOGLE_MAPS_API_KEY;
if (apiKey) {
  console.log('API Key format check:');
  console.log('- Starts with AIza:', apiKey.startsWith('AIza'));
  console.log('- Length is 39:', apiKey.length === 39);
  console.log('- Contains only valid characters:', /^[A-Za-z0-9_-]+$/.test(apiKey));
} else {
  console.log('❌ API Key not found');
}
console.groupEnd();

// 9. Check Console Errors
console.group('🐛 Console Error Summary');
const originalError = console.error;
const errors = [];
console.error = function(...args) {
  errors.push(args.join(' '));
  originalError.apply(console, args);
};

setTimeout(() => {
  console.log('Errors captured in last 5 seconds:', errors.length);
  errors.forEach((error, index) => {
    console.log(`Error ${index}:`, error);
  });
  console.error = originalError;
}, 5000);
console.groupEnd();

console.log('🔍 Diagnostic complete. Check the groups above for details.');
